﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using ProManage.Modules.Helpers.ParametersForm;
using ProManage.Modules.Data.ParametersForm;
using ProManage.Modules.Services;

namespace ProManage.Forms
{
    public partial class ParametersForm : Form
    {
        /// <summary>
        /// DataTable for grid binding
        /// </summary>
        public DataTable GridDataTable { get; set; }

        public ParametersForm()
        {
            InitializeComponent();
        }

        private void ProgramParameterForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize the grid with columns
                ParametersFormHelper.InitializeGrid(this);

                // Load parameters from database
                ParametersFormHelper.LoadParametersToGrid(this);

                // Setup event handlers
                SetupEventHandlers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading Parameters form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up event handlers for ribbon buttons
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // New button event handler
                barButtonItem1.ItemClick += BarButtonItemNew_ItemClick;

                // Edit button event handler (placeholder for future implementation)
                barButtonItem2.ItemClick += BarButtonItemEdit_ItemClick;

                // Delete button event handler
                barButtonItem3.ItemClick += BarButtonItemDelete_ItemClick;

                // Save button event handler
                barButtonItem4.ItemClick += BarButtonItemSave_ItemClick;

                Debug.WriteLine("Event handlers setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles New button click - opens parameter entry popup dialog
        /// </summary>
        private void BarButtonItemNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== New button clicked - Opening ParamEntryForm ===");

                // Create and show the parameter entry popup form
                using (var paramEntryForm = new ParamEntryForm())
                {
                    // Show as modal dialog
                    var result = paramEntryForm.ShowDialog(this);

                    if (result == DialogResult.OK)
                    {
                        Debug.WriteLine($"Parameter saved successfully with ID: {paramEntryForm.NewParameterId}");

                        // Refresh parameter cache after new parameter addition
                        Debug.WriteLine("Refreshing parameter cache after new parameter addition");
                        bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                        if (cacheRefreshed)
                        {
                            Debug.WriteLine("Parameter cache refreshed successfully");
                        }
                        else
                        {
                            Debug.WriteLine("Warning: Parameter cache refresh failed");
                        }

                        // Refresh the grid to show the new parameter
                        ParametersFormHelper.RefreshParametersGrid(this);

                        Debug.WriteLine("Grid refreshed after new parameter addition");
                    }
                    else
                    {
                        Debug.WriteLine("Parameter entry was cancelled by user");
                    }
                }

                Debug.WriteLine("=== New button click completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening parameter entry form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click - opens parameter entry popup dialog for editing
        /// </summary>
        private void BarButtonItemEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Edit button clicked ===");

                // Force grid to post any pending edits
                gridView1.PostEditor();
                gridView1.UpdateCurrentRow();

                // Get the selected parameter
                var selectedParameter = ParametersFormHelper.GetSelectedParameter(this);

                if (selectedParameter == null)
                {
                    MessageBox.Show("Please select a parameter to edit by checking the checkbox in the 'Select' column.",
                        "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"Selected parameter for editing: ID {selectedParameter.Id} - {selectedParameter.ParameterCode}");

                // Create and setup the parameter entry form for editing
                using (var paramEntryForm = new ParamEntryForm())
                {
                    // Setup the form for editing
                    paramEntryForm.SetupForEdit(selectedParameter);

                    // Show as modal dialog
                    var result = paramEntryForm.ShowDialog(this);

                    if (result == DialogResult.OK)
                    {
                        Debug.WriteLine($"Parameter updated successfully: {selectedParameter.ParameterCode}");

                        // Refresh parameter cache after parameter update
                        Debug.WriteLine("Refreshing parameter cache after parameter update");
                        bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                        if (cacheRefreshed)
                        {
                            Debug.WriteLine("Parameter cache refreshed successfully");
                        }
                        else
                        {
                            Debug.WriteLine("Warning: Parameter cache refresh failed");
                        }

                        // Refresh the grid to show the updated parameter
                        ParametersFormHelper.RefreshParametersGrid(this);

                        Debug.WriteLine("Grid refreshed after parameter update");
                    }
                    else
                    {
                        Debug.WriteLine("Parameter edit was cancelled by user");
                    }
                }

                Debug.WriteLine("=== Edit button click completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button click: {ex.Message}");
                MessageBox.Show($"Error in edit operation: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click - deletes selected parameters
        /// </summary>
        private void BarButtonItemDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Delete button clicked ===");

                // Force grid to post any pending edits
                gridView1.PostEditor();
                gridView1.UpdateCurrentRow();

                // Get the selected parameter
                var selectedParameter = ParametersFormHelper.GetSelectedParameter(this);

                if (selectedParameter == null)
                {
                    MessageBox.Show("Please select a parameter to delete by checking the checkbox in the 'Select' column.",
                        "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"Selected parameter for deletion: ID {selectedParameter.Id} - {selectedParameter.ParameterCode}");

                // Check if this is a new unsaved parameter
                if (selectedParameter.Id <= 0)
                {
                    MessageBox.Show("This parameter has not been saved to the database yet. Use the grid's checkbox to remove it from the list.",
                        "Cannot Delete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Confirm deletion
                var result = MessageBox.Show($"Are you sure you want to delete the parameter '{selectedParameter.ParameterCode}'?\n\nThis action cannot be undone.",
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    Debug.WriteLine("Parameter deletion was cancelled by user");
                    return;
                }

                // Delete from database
                var deleteIds = new List<int> { selectedParameter.Id };
                int deletedCount = ParametersFormRepository.DeleteParameters(deleteIds);

                if (deletedCount > 0)
                {
                    Debug.WriteLine($"Parameter deleted successfully from database: {selectedParameter.ParameterCode}");

                    // Refresh parameter cache after parameter deletion
                    Debug.WriteLine("Refreshing parameter cache after parameter deletion");
                    bool cacheRefreshed = ParameterCacheService.Instance.RefreshCache();
                    if (cacheRefreshed)
                    {
                        Debug.WriteLine("Parameter cache refreshed successfully");
                    }
                    else
                    {
                        Debug.WriteLine("Warning: Parameter cache refresh failed");
                    }

                    // Refresh the grid to show updated data
                    ParametersFormHelper.RefreshParametersGrid(this);

                    MessageBox.Show($"Parameter '{selectedParameter.ParameterCode}' deleted successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    Debug.WriteLine("Grid refreshed after parameter deletion");
                }
                else
                {
                    MessageBox.Show("Failed to delete parameter. Please try again.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                Debug.WriteLine("=== Delete button click completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button click: {ex.Message}");
                MessageBox.Show($"Error deleting parameter: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Save button click - saves all changes to database
        /// </summary>
        private void BarButtonItemSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Save button clicked");
                ParametersFormHelper.SaveParameters(this);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Save button click: {ex.Message}");
                MessageBox.Show($"Error saving parameters: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles form closing - save any pending changes
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // Check if there are unsaved changes
                if (HasUnsavedChanges())
                {
                    var result = MessageBox.Show("You have unsaved changes. Do you want to save them before closing?",
                        "Unsaved Changes", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        ParametersFormHelper.SaveParameters(this);
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        e.Cancel = true;
                        return;
                    }
                }

                base.OnFormClosing(e);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in OnFormClosing: {ex.Message}");
                base.OnFormClosing(e);
            }
        }

        /// <summary>
        /// Checks if there are unsaved changes in the grid
        /// </summary>
        /// <returns>True if there are unsaved changes</returns>
        private bool HasUnsavedChanges()
        {
            try
            {
                if (GridDataTable == null)
                    return false;

                // Check for new rows (Id = 0) with data
                foreach (DataRow row in GridDataTable.Rows)
                {
                    int id = Convert.ToInt32(row["Id"]);
                    string paramCode = row["ParameterCode"]?.ToString()?.Trim();
                    string paramValue = row["ParameterValue"]?.ToString()?.Trim();

                    if (id == 0 && (!string.IsNullOrWhiteSpace(paramCode) || !string.IsNullOrWhiteSpace(paramValue)))
                    {
                        return true; // New row with data
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
