using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Diagnostics;
using ProManage.Modules.Data.UserMasterForm;
using ProManage.Modules.Models.UserMasterForm;
using ProManage.Modules.Services;
using ProManage.Modules.Connections;
using ProManage.Modules.Helpers.UserMasterForm;

namespace ProManage.Forms
{
    public partial class UserMasterForm : Form
    {
        #region Private Fields

        private UserMasterFormModel _currentUser;
        private bool _isNewMode = false;
        private bool _isDirty = false;
        private bool _isViewMode = false;

        #endregion

        #region Constructor

        public UserMasterForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Initializes the form
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // Set form properties
                this.Text = "User Entry";

                // Wire up ribbon button events
                WireUpRibbonEvents();

                // Wire up photo button events
                btnBrowsePhoto.Click += btnBrowsePhoto_Click;
                btnRemovePhoto.Click += btnRemovePhoto_Click;

                // Wire up text change events for dirty tracking
                WireUpDirtyTrackingEvents();

                // Configure password field security
                ConfigurePasswordSecurity();

                // Initialize button states
                SetButtonStates();

                // Initialize with new user
                NewUser();

                Debug.WriteLine("UserMasterForm initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing UserMasterForm: {ex.Message}");
                MessageBox.Show($"Error initializing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Wires up ribbon button events
        /// </summary>
        private void WireUpRibbonEvents()
        {
            try
            {
                // Wire up all ribbon button events
                BarButtonItemNew.ItemClick += btnNew_ItemClick;
                BarButtonItemEdit.ItemClick += btnEdit_ItemClick;
                BarButtonItemSave.ItemClick += btnSave_ItemClick;
                BarButtonItemCancel.ItemClick += btnCancel_ItemClick;
                BarButtonItemDelete.ItemClick += btnDelete_ItemClick;
                BarButtonItemFirst.ItemClick += btnFirst_ItemClick;
                BarButtonItemPrevious.ItemClick += btnPrevious_ItemClick;
                BarButtonItemNext.ItemClick += btnNext_ItemClick;
                BarButtonItemLast.ItemClick += btnLast_ItemClick;
                barButtonItem2.ItemClick += btnFind_ItemClick; // Find button
                BarButtonItemPrint.ItemClick += btnPrint_ItemClick;

                Debug.WriteLine("Ribbon button events wired up successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error wiring up ribbon events: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Wires up events for dirty tracking
        /// </summary>
        private void WireUpDirtyTrackingEvents()
        {
            try
            {
                // Text change events
                txtUsername.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtFullName.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtShortName.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtEmail.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtPhone.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtDesignation.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtPassword.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtEditPassword.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                txtConfirmPassword.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };

                // Combo box change events
                cmbRole.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };
                cmbDepartment.TextChanged += (s, e) => { _isDirty = true; SetButtonStates(); };

                // Checkbox change events
                chkIsActive.CheckedChanged += (s, e) => { _isDirty = true; SetButtonStates(); };

                Debug.WriteLine("Dirty tracking events wired up successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error wiring up dirty tracking events: {ex.Message}");
            }
        }

        /// <summary>
        /// Configures password field security settings
        /// </summary>
        private void ConfigurePasswordSecurity()
        {
            try
            {
                // Set password character for all password fields using DevExpress property
                txtPassword.Properties.UseSystemPasswordChar = true;
                txtEditPassword.Properties.UseSystemPasswordChar = true;
                txtConfirmPassword.Properties.UseSystemPasswordChar = true;

                // Add event handlers to clear placeholder text when user starts typing
                txtPassword.Enter += PasswordField_Enter;
                txtEditPassword.Enter += PasswordField_Enter;
                txtConfirmPassword.Enter += PasswordField_Enter;

                Debug.WriteLine("Password field security configured successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error configuring password security: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles password field enter event to clear placeholder text
        /// </summary>
        private void PasswordField_Enter(object sender, EventArgs e)
        {
            try
            {
                if (sender is DevExpress.XtraEditors.TextEdit textEdit)
                {
                    // Clear placeholder text when user enters the field
                    if (textEdit.Text == "••••••••")
                    {
                        textEdit.Text = string.Empty;
                        Debug.WriteLine($"Placeholder cleared for password field: {textEdit.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling password field enter: {ex.Message}");
            }
        }

        #endregion

        #region User Management

        /// <summary>
        /// Creates a new user
        /// </summary>
        private void NewUser()
        {
            try
            {
                _currentUser = new UserMasterFormModel();
                _isNewMode = true;
                _isDirty = false;

                // Clear all form fields
                ClearForm();

                // Set button states
                SetButtonStates();

                Debug.WriteLine("New user created");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating new user: {ex.Message}");
                MessageBox.Show($"Error creating new user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Clears all form fields
        /// </summary>
        private void ClearForm()
        {
            try
            {
                // Clear text fields
                txtUsername.Text = string.Empty;
                txtFullName.Text = string.Empty;
                txtShortName.Text = string.Empty;
                txtEmail.Text = string.Empty;
                txtPhone.Text = string.Empty;
                txtDesignation.Text = string.Empty;
                txtPassword.Text = string.Empty;
                txtEditPassword.Text = string.Empty;
                txtConfirmPassword.Text = string.Empty;

                // Reset combo boxes
                cmbRole.Text = "User";
                cmbDepartment.Text = string.Empty;

                // Reset checkbox
                chkIsActive.Checked = true;

                // Clear photo
                pictureUser.Image = null;

                Debug.WriteLine("Form cleared");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing form: {ex.Message}");
            }
        }

        #endregion

        #region Button State Management

        /// <summary>
        /// Sets the button states based on current mode
        /// </summary>
        private void SetButtonStates()
        {
            try
            {
                BarButtonItemNew.Enabled = true;

                // Edit button - enabled only in VIEW mode for existing users
                if (BarButtonItemEdit != null)
                {
                    BarButtonItemEdit.Enabled = _isViewMode && !_isNewMode && _currentUser?.UserId > 0;
                }

                // Handle Save/Cancel/Delete buttons based on mode
                if (_isViewMode)
                {
                    // VIEW mode: Save/Cancel disabled, Delete enabled for existing users
                    BarButtonItemSave.Enabled = false;
                    BarButtonItemCancel.Enabled = false;

                    if (BarButtonItemDelete != null)
                    {
                        BarButtonItemDelete.Enabled = !_isNewMode && _currentUser?.UserId > 0;
                    }

                    Debug.WriteLine("Button states set for VIEW mode");
                }
                else
                {
                    // EDIT mode: Save/Cancel based on dirty state, Delete disabled
                    BarButtonItemSave.Enabled = _isDirty;
                    BarButtonItemCancel.Enabled = _isDirty;

                    if (BarButtonItemDelete != null)
                    {
                        BarButtonItemDelete.Enabled = false; // Disabled in edit mode
                    }

                    Debug.WriteLine("Button states set for EDIT mode");
                }

                // Navigation buttons - enabled when not in new mode
                BarButtonItemFirst.Enabled = !_isNewMode;
                BarButtonItemPrevious.Enabled = !_isNewMode;
                BarButtonItemNext.Enabled = !_isNewMode;
                BarButtonItemLast.Enabled = !_isNewMode;
                barButtonItem2.Enabled = !_isNewMode; // Find button

                BarButtonItemPrint.Enabled = !_isNewMode && _currentUser?.UserId > 0;

                Debug.WriteLine($"Button states updated - NewMode: {_isNewMode}, ViewMode: {_isViewMode}, Dirty: {_isDirty}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting button states: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets the field states based on current mode
        /// </summary>
        /// <param name="enableFields">True to enable fields for editing, false for view mode</param>
        private void SetFieldStates(bool enableFields)
        {
            try
            {
                // Enable/disable all form fields except tab control
                txtUsername.Enabled = enableFields;
                txtFullName.Enabled = enableFields;
                txtShortName.Enabled = enableFields;
                txtEmail.Enabled = enableFields;
                txtPhone.Enabled = enableFields;
                txtDesignation.Enabled = enableFields;
                txtPassword.Enabled = enableFields;
                txtEditPassword.Enabled = enableFields;
                txtConfirmPassword.Enabled = enableFields;

                // Combo boxes
                cmbRole.Enabled = enableFields;
                cmbDepartment.Enabled = enableFields;

                // Checkbox
                chkIsActive.Enabled = enableFields;

                // Photo buttons
                btnBrowsePhoto.Enabled = enableFields;
                btnRemovePhoto.Enabled = enableFields;

                // NEVER disable tab control - users should always be able to navigate between tabs
                // tabControl remains enabled for navigation

                Debug.WriteLine($"Field states set - EnableFields: {enableFields}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting field states: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles New button click
        /// </summary>
        private void btnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                SetNewUserMode();
                Debug.WriteLine("New button clicked");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button: {ex.Message}");
                MessageBox.Show($"Error creating new user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click
        /// </summary>
        private void btnEdit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Edit button clicked");

                // Only allow editing of existing users in VIEW mode
                if (_isNewMode)
                {
                    MessageBox.Show("Cannot edit a new user. Please save the user first.", "Cannot Edit",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (_currentUser?.UserId <= 0)
                {
                    MessageBox.Show("No user selected for editing.", "Cannot Edit",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (!_isViewMode)
                {
                    MessageBox.Show("User is already in edit mode.", "Already Editing",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Switch from VIEW mode to EDIT mode
                _isViewMode = false;
                _isDirty = false; // Reset dirty flag when entering edit mode

                // Enable fields for editing
                SetFieldStates(true);
                SetButtonStates();

                Debug.WriteLine($"Switched to EDIT mode for user: {_currentUser.Username}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button: {ex.Message}");
                MessageBox.Show($"Error switching to edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Save button click
        /// </summary>
        private void btnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Save button clicked ===");
                Debug.WriteLine($"Current mode: {(_isNewMode ? "New" : "Edit")}, User ID: {_currentUser?.UserId}");

                // Step 1: Validate form data
                Debug.WriteLine("Step 1: Starting form validation...");
                if (!ValidateFormDataWithDetails())
                {
                    Debug.WriteLine("Form validation failed - stopping save process");
                    return;
                }
                Debug.WriteLine("Form validation passed");

                // Step 2: Save form data to user model
                Debug.WriteLine("Step 2: Saving form data to user model...");
                if (!SaveFormDataToUserWithValidation())
                {
                    Debug.WriteLine("Failed to save form data to user model");
                    return;
                }
                Debug.WriteLine($"User model updated - Username: {_currentUser.Username}, Email: {_currentUser.Email}");

                // Step 3: Test database connection
                Debug.WriteLine("Step 3: Testing database connection...");
                if (!TestDatabaseConnection())
                {
                    MessageBox.Show("Database connection failed. Please check your connection settings.", "Database Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                Debug.WriteLine("Database connection successful");

                // Step 4: Save to database
                Debug.WriteLine("Step 4: Saving user to database...");
                bool success = SaveUserToDatabase();
                if (!success)
                {
                    Debug.WriteLine("Database save failed");
                    return;
                }
                Debug.WriteLine($"Database save successful - User ID: {_currentUser.UserId}");

                // Step 5: Handle edit password separately if provided (and not placeholder)
                if (!string.IsNullOrWhiteSpace(txtEditPassword.Text) && txtEditPassword.Text != "••••••••")
                {
                    Debug.WriteLine("Step 5: Updating edit password...");
                    if (!SaveEditPassword())
                    {
                        Debug.WriteLine("Edit password update failed, but user was saved");
                        MessageBox.Show("User saved successfully, but edit password update failed.", "Partial Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                    else
                    {
                        Debug.WriteLine("Edit password updated successfully");
                    }
                }

                // Step 6: Update form state
                Debug.WriteLine("Step 6: Updating form state...");
                _isDirty = false;
                _isNewMode = false;
                SetButtonStates();

                // Set dialog result for form navigation
                this.DialogResult = DialogResult.OK;

                MessageBox.Show("User saved successfully.", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                Debug.WriteLine($"=== Save completed successfully - User ID: {_currentUser.UserId} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== CRITICAL ERROR in Save button: {ex.Message} ===");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Critical error saving user: {ex.Message}\n\nPlease check the debug output for details.", "Critical Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click
        /// </summary>
        private void btnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (_isDirty)
                {
                    var result = MessageBox.Show(
                        "You have unsaved changes. Are you sure you want to cancel?",
                        "Unsaved Changes",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                    {
                        return;
                    }
                }

                // Clear form and reset to new user mode
                ClearForm();
                _currentUser = new UserMasterFormModel();
                _isNewMode = true;
                _isDirty = false;
                SetButtonStates();

                // Set dialog result and close if opened as dialog
                this.DialogResult = DialogResult.Cancel;
                this.Close();

                Debug.WriteLine("Changes cancelled and form reset");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Cancel button: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles First button click
        /// </summary>
        private void btnFirst_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("First button clicked");
                NavigateToFirst();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in First button: {ex.Message}");
                MessageBox.Show($"Error navigating to first user: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Previous button click
        /// </summary>
        private void btnPrevious_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Previous button clicked");
                NavigateToPrevious();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Previous button: {ex.Message}");
                MessageBox.Show($"Error navigating to previous user: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Next button click
        /// </summary>
        private void btnNext_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Next button clicked");
                NavigateToNext();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Next button: {ex.Message}");
                MessageBox.Show($"Error navigating to next user: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Last button click
        /// </summary>
        private void btnLast_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Last button clicked");
                NavigateToLast();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Last button: {ex.Message}");
                MessageBox.Show($"Error navigating to last user: {ex.Message}", "Navigation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Find button click
        /// </summary>
        private void btnFind_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                // TODO: Open find/search dialog
                Debug.WriteLine("Find button clicked");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Find button: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles Print button click
        /// </summary>
        private void btnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                // TODO: Print current user details
                Debug.WriteLine("Print button clicked");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Print button: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles Delete button click
        /// </summary>
        private void btnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Delete button clicked");

                // Only allow deletion of existing users
                if (_isNewMode || _currentUser?.UserId <= 0)
                {
                    MessageBox.Show("Cannot delete a new user that hasn't been saved yet.", "Cannot Delete",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Confirm deletion
                var result = MessageBox.Show(
                    $"Are you sure you want to delete user '{_currentUser.FullName}'?\n\nThis will deactivate the user account and cannot be undone.",
                    "Confirm Delete",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    // Store current user ID for navigation after deletion
                    int deletedUserId = _currentUser.UserId;
                    string deletedUsername = _currentUser.Username;

                    // Perform soft delete (set is_active = false)
                    bool success = UserMasterFormRepository.DeleteUser(_currentUser.UserId);
                    if (success)
                    {
                        MessageBox.Show("User deleted successfully.", "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        Debug.WriteLine($"User {deletedUsername} deleted successfully");

                        // Navigate to next user after deletion
                        try
                        {
                            var nextUser = UserMasterFormNavigation.NavigateToNext(deletedUserId);
                            if (nextUser != null)
                            {
                                LoadUserForNavigation(nextUser);
                                UserMasterFormNavigation.UpdateNavigationPosition(this, nextUser);
                                Debug.WriteLine($"Navigated to next user after deletion: {nextUser.Username}");
                            }
                            else
                            {
                                // No next user, try previous
                                var previousUser = UserMasterFormNavigation.NavigateToPrevious(deletedUserId);
                                if (previousUser != null)
                                {
                                    LoadUserForNavigation(previousUser);
                                    UserMasterFormNavigation.UpdateNavigationPosition(this, previousUser);
                                    Debug.WriteLine($"Navigated to previous user after deletion: {previousUser.Username}");
                                }
                                else
                                {
                                    // No users left, set to new mode
                                    SetNewUserMode();
                                    Debug.WriteLine("No users left after deletion, switched to new user mode");
                                }
                            }
                        }
                        catch (Exception navEx)
                        {
                            Debug.WriteLine($"Error navigating after deletion: {navEx.Message}");
                            // If navigation fails, just set to new mode
                            SetNewUserMode();
                        }
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete user.", "Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button: {ex.Message}");
                MessageBox.Show($"Error deleting user: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Browse Photo button click
        /// </summary>
        private void btnBrowsePhoto_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                    openFileDialog.Title = "Select User Photo";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        pictureUser.Image = Image.FromFile(openFileDialog.FileName);
                        _isDirty = true;
                        SetButtonStates();

                        Debug.WriteLine($"Photo loaded: {openFileDialog.FileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading photo: {ex.Message}");
                MessageBox.Show($"Error loading photo: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Remove Photo button click
        /// </summary>
        private void btnRemovePhoto_Click(object sender, EventArgs e)
        {
            try
            {
                if (pictureUser.Image != null)
                {
                    var result = MessageBox.Show(
                        "Are you sure you want to remove the user photo?",
                        "Remove Photo",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        pictureUser.Image = null;
                        _isDirty = true;
                        SetButtonStates();

                        Debug.WriteLine("Photo removed");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing photo: {ex.Message}");
                MessageBox.Show($"Error removing photo: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Public Methods for Form Navigation

        /// <summary>
        /// Sets the form to new user mode
        /// </summary>
        public void SetNewUserMode()
        {
            try
            {
                _currentUser = new UserMasterFormModel();
                _isNewMode = true;
                _isViewMode = false;
                _isDirty = false;

                ClearForm();
                SetFieldStates(true); // Enable fields for new user entry
                SetButtonStates();

                // Set default values
                chkIsActive.Checked = true;
                cmbRole.Text = "User";

                Debug.WriteLine("Form set to new user mode");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting new user mode: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets the form to view mode with the specified user
        /// </summary>
        /// <param name="user">User to view</param>
        public void SetUserForViewing(UserMasterFormModel user)
        {
            try
            {
                if (user == null)
                {
                    throw new ArgumentNullException(nameof(user));
                }

                // Load the complete user data from database
                _currentUser = UserMasterFormRepository.GetUserById(user.UserId);
                if (_currentUser == null)
                {
                    throw new Exception($"User with ID {user.UserId} not found in database");
                }

                _isNewMode = false;
                _isViewMode = true;
                _isDirty = false;

                LoadUserDataToForm();
                SetFieldStates(false); // Disable fields for view mode
                SetButtonStates();
                UserMasterFormNavigation.UpdateNavigationPosition(this, _currentUser);

                Debug.WriteLine($"Form set to VIEW mode for user: {_currentUser.Username}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting user for viewing: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Sets the form to edit mode with the specified user
        /// </summary>
        /// <param name="user">User to edit</param>
        public void SetUserForEditing(UserMasterFormModel user)
        {
            try
            {
                if (user == null)
                {
                    throw new ArgumentNullException(nameof(user));
                }

                // Load the complete user data from database
                _currentUser = UserMasterFormRepository.GetUserById(user.UserId);
                if (_currentUser == null)
                {
                    throw new Exception($"User with ID {user.UserId} not found in database");
                }

                _isNewMode = false;
                _isViewMode = false;
                _isDirty = false;

                LoadUserDataToForm();
                SetFieldStates(true); // Enable fields for edit mode
                SetButtonStates();
                UserMasterFormNavigation.UpdateNavigationPosition(this, _currentUser);

                Debug.WriteLine($"Form set to EDIT mode for user: {_currentUser.Username}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting user for editing: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Navigation Methods

        /// <summary>
        /// Navigates to the first user
        /// </summary>
        private void NavigateToFirst()
        {
            try
            {
                var user = UserMasterFormNavigation.NavigateToFirst();
                if (user != null)
                {
                    LoadUserForNavigation(user);
                    UserMasterFormNavigation.UpdateNavigationPosition(this, user);
                    Debug.WriteLine($"Navigated to first user: {user.Username}");
                }
                else
                {
                    MessageBox.Show("No users found in the database.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to first user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Navigates to the previous user
        /// </summary>
        private void NavigateToPrevious()
        {
            try
            {
                if (_currentUser?.UserId == null || _currentUser.UserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var user = UserMasterFormNavigation.NavigateToPrevious(_currentUser.UserId);
                if (user != null)
                {
                    LoadUserForNavigation(user);
                    UserMasterFormNavigation.UpdateNavigationPosition(this, user);
                    Debug.WriteLine($"Navigated to previous user: {user.Username}");
                }
                else
                {
                    MessageBox.Show("Already at the first user.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to previous user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Navigates to the next user
        /// </summary>
        private void NavigateToNext()
        {
            try
            {
                if (_currentUser?.UserId == null || _currentUser.UserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var user = UserMasterFormNavigation.NavigateToNext(_currentUser.UserId);
                if (user != null)
                {
                    LoadUserForNavigation(user);
                    UserMasterFormNavigation.UpdateNavigationPosition(this, user);
                    Debug.WriteLine($"Navigated to next user: {user.Username}");
                }
                else
                {
                    MessageBox.Show("Already at the last user.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to next user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Navigates to the last user
        /// </summary>
        private void NavigateToLast()
        {
            try
            {
                var user = UserMasterFormNavigation.NavigateToLast();
                if (user != null)
                {
                    LoadUserForNavigation(user);
                    UserMasterFormNavigation.UpdateNavigationPosition(this, user);
                    Debug.WriteLine($"Navigated to last user: {user.Username}");
                }
                else
                {
                    MessageBox.Show("No users found in the database.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to last user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Loads a user for navigation while maintaining current mode
        /// </summary>
        /// <param name="user">User to load</param>
        private void LoadUserForNavigation(UserMasterFormModel user)
        {
            try
            {
                _currentUser = user;
                _isNewMode = false;
                _isDirty = false;

                LoadUserDataToForm();
                SetButtonStates();

                Debug.WriteLine($"User loaded for navigation: {user.Username} (Mode: {(_isViewMode ? "VIEW" : "EDIT")})");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading user for navigation: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Form Data Management

        /// <summary>
        /// Loads user data into form controls
        /// </summary>
        private void LoadUserDataToForm()
        {
            try
            {
                if (_currentUser == null) return;

                // General tab
                txtUsername.Text = _currentUser.Username ?? string.Empty;
                txtFullName.Text = _currentUser.FullName ?? string.Empty;
                txtShortName.Text = _currentUser.ShortName ?? string.Empty;
                txtEmail.Text = _currentUser.Email ?? string.Empty;
                txtPhone.Text = _currentUser.Phone ?? string.Empty;
                cmbRole.Text = _currentUser.Role ?? "User";
                cmbDepartment.Text = _currentUser.Department ?? string.Empty;
                txtDesignation.Text = _currentUser.Designation ?? string.Empty;
                chkIsActive.Checked = _currentUser.IsActive;

                // Handle password fields - show placeholder for existing passwords
                LoadPasswordFieldsWithPlaceholders();

                // Load photo if available
                LoadUserPhoto();

                Debug.WriteLine("User data loaded to form successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading user data to form: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Loads password fields with appropriate placeholders for existing passwords
        /// </summary>
        private void LoadPasswordFieldsWithPlaceholders()
        {
            try
            {
                // For existing users, show placeholder text to indicate password exists
                if (!_isNewMode && _currentUser?.UserId > 0)
                {
                    // Check if login password exists (PasswordHash and PasswordSalt)
                    bool hasLoginPassword = !string.IsNullOrWhiteSpace(_currentUser.PasswordHash) &&
                                          !string.IsNullOrWhiteSpace(_currentUser.PasswordSalt);

                    if (hasLoginPassword)
                    {
                        txtPassword.Text = "••••••••";
                        txtConfirmPassword.Text = "••••••••";
                        Debug.WriteLine("Login password placeholder set");
                    }
                    else
                    {
                        txtPassword.Text = string.Empty;
                        txtConfirmPassword.Text = string.Empty;
                        Debug.WriteLine("No login password found");
                    }

                    // For edit password, we need to check if it exists in database
                    // Since we don't store edit password in the model, we'll check if user has any edit password
                    bool hasEditPassword = CheckIfEditPasswordExists(_currentUser.UserId);

                    if (hasEditPassword)
                    {
                        txtEditPassword.Text = "••••••••";
                        Debug.WriteLine("Edit password placeholder set");
                    }
                    else
                    {
                        txtEditPassword.Text = string.Empty;
                        Debug.WriteLine("No edit password found");
                    }
                }
                else
                {
                    // For new users, clear all password fields
                    txtPassword.Text = string.Empty;
                    txtEditPassword.Text = string.Empty;
                    txtConfirmPassword.Text = string.Empty;
                    Debug.WriteLine("Password fields cleared for new user");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading password fields: {ex.Message}");
                // Fallback to clearing fields
                txtPassword.Text = string.Empty;
                txtEditPassword.Text = string.Empty;
                txtConfirmPassword.Text = string.Empty;
            }
        }

        /// <summary>
        /// Checks if edit password exists for the user
        /// </summary>
        /// <param name="userId">User ID to check</param>
        /// <returns>True if edit password exists</returns>
        private bool CheckIfEditPasswordExists(int userId)
        {
            try
            {
                // This would typically query the database to check if edit password exists
                // For now, we'll assume it exists if the user has a login password
                // This can be enhanced later with a proper database query
                // Note: userId parameter is reserved for future database query implementation
                _ = userId; // Suppress unused parameter warning
                return !string.IsNullOrWhiteSpace(_currentUser?.PasswordHash);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking edit password existence: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves form data to user model
        /// </summary>
        private void SaveFormDataToUser()
        {
            try
            {
                if (_currentUser == null) return;

                // General tab
                _currentUser.Username = txtUsername.Text?.Trim();
                _currentUser.FullName = txtFullName.Text?.Trim();
                _currentUser.ShortName = txtShortName.Text?.Trim();
                _currentUser.Email = txtEmail.Text?.Trim();
                _currentUser.Phone = txtPhone.Text?.Trim();
                _currentUser.Role = cmbRole.Text?.Trim();
                _currentUser.Department = cmbDepartment.Text?.Trim();
                _currentUser.Designation = txtDesignation.Text?.Trim();
                _currentUser.IsActive = chkIsActive.Checked;

                // Handle passwords
                HandlePasswordUpdates();

                // Handle photo
                SaveUserPhoto();

                Debug.WriteLine("Form data saved to user model successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving form data to user: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Handles password updates based on form input
        /// </summary>
        private void HandlePasswordUpdates()
        {
            try
            {
                // Handle login password (hash + salt)
                // Check if user entered a new password (not placeholder)
                if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text != "••••••••")
                {
                    if (txtPassword.Text != txtConfirmPassword.Text)
                    {
                        throw new Exception("Password and confirm password do not match");
                    }

                    PasswordSecurityService.GenerateLoginPasswordHash(
                        txtPassword.Text,
                        out string hash,
                        out string salt);

                    _currentUser.PasswordHash = hash;
                    _currentUser.PasswordSalt = salt;

                    Debug.WriteLine("Login password updated");
                }
                else if (_isNewMode)
                {
                    // For new users, ensure we have default values if no password provided
                    // This should not happen due to validation, but safety check
                    _currentUser.PasswordHash = string.Empty;
                    _currentUser.PasswordSalt = string.Empty;
                    Debug.WriteLine("Warning: New user with no password - using empty values");
                }
                // If password field contains placeholder, don't update (keep existing password)

                // Handle edit password (hash only)
                // Check if user entered a new edit password (not placeholder)
                if (!string.IsNullOrWhiteSpace(txtEditPassword.Text) && txtEditPassword.Text != "••••••••")
                {
                    // For edit password, we'll update it separately after saving the user
                    Debug.WriteLine("Edit password will be updated separately");
                }
                // If edit password field contains placeholder, don't update (keep existing password)
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling password updates: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Loads user photo from file path
        /// </summary>
        private void LoadUserPhoto()
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(_currentUser?.PhotoPath) &&
                    System.IO.File.Exists(_currentUser.PhotoPath))
                {
                    pictureUser.Image = Image.FromFile(_currentUser.PhotoPath);
                    Debug.WriteLine($"User photo loaded: {_currentUser.PhotoPath}");
                }
                else
                {
                    pictureUser.Image = null;
                    Debug.WriteLine("No user photo to load");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading user photo: {ex.Message}");
                pictureUser.Image = null;
            }
        }

        /// <summary>
        /// Saves user photo to file and updates path
        /// </summary>
        private void SaveUserPhoto()
        {
            try
            {
                if (pictureUser.Image != null)
                {
                    // Create photos directory if it doesn't exist
                    string photosDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Photos");
                    if (!Directory.Exists(photosDir))
                    {
                        Directory.CreateDirectory(photosDir);
                    }

                    // Generate unique filename - use timestamp if UserId is 0 (new user)
                    string userIdentifier = _currentUser.UserId > 0 ? _currentUser.UserId.ToString() : "new";
                    string fileName = $"user_{userIdentifier}_{DateTime.Now:yyyyMMdd_HHmmss}.jpg";
                    string filePath = Path.Combine(photosDir, fileName);

                    // Save image
                    pictureUser.Image.Save(filePath, System.Drawing.Imaging.ImageFormat.Jpeg);
                    _currentUser.PhotoPath = filePath;

                    Debug.WriteLine($"User photo saved: {filePath}");
                }
                else
                {
                    _currentUser.PhotoPath = null;
                    Debug.WriteLine("No photo to save");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving user photo: {ex.Message}");
                // Don't throw - photo saving is not critical
            }
        }

        /// <summary>
        /// Validates form data
        /// </summary>
        private bool ValidateFormData()
        {
            try
            {
                var errors = new List<string>();

                // Required fields
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                    errors.Add("Username is required");

                if (string.IsNullOrWhiteSpace(txtFullName.Text))
                    errors.Add("Full name is required");

                if (string.IsNullOrWhiteSpace(txtEmail.Text))
                    errors.Add("Email is required");

                if (string.IsNullOrWhiteSpace(cmbDepartment.Text))
                    errors.Add("Department is required");

                if (string.IsNullOrWhiteSpace(txtShortName.Text))
                    errors.Add("Short name is required");

                // Email format validation
                if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
                    errors.Add("Invalid email format");

                // Password validation for new users
                if (_isNewMode && (string.IsNullOrWhiteSpace(txtPassword.Text) || txtPassword.Text == "••••••••"))
                    errors.Add("Login password is required for new users");

                if (_isNewMode && (string.IsNullOrWhiteSpace(txtEditPassword.Text) || txtEditPassword.Text == "••••••••"))
                    errors.Add("Edit password is required for new users");

                // Password confirmation (only validate if user entered new password)
                if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text != "••••••••" &&
                    txtPassword.Text != txtConfirmPassword.Text)
                    errors.Add("Password and confirm password do not match");

                // Password strength (only validate if user entered new password)
                if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text != "••••••••" &&
                    !PasswordSecurityService.ValidatePasswordStrength(txtPassword.Text))
                    errors.Add("Login password does not meet minimum requirements (at least 6 characters)");

                // Edit password strength (only validate if user entered new edit password)
                if (!string.IsNullOrWhiteSpace(txtEditPassword.Text) && txtEditPassword.Text != "••••••••" &&
                    !PasswordSecurityService.ValidatePasswordStrength(txtEditPassword.Text))
                    errors.Add("Edit password does not meet minimum requirements (at least 6 characters)");

                if (errors.Count > 0)
                {
                    string errorMessage = "Please correct the following errors:\n\n" + string.Join("\n", errors);
                    MessageBox.Show(errorMessage, "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating form data: {ex.Message}");
                MessageBox.Show($"Error validating form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Validates email format
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Enhanced Save Process Methods

        /// <summary>
        /// Validates form data with detailed logging
        /// </summary>
        private bool ValidateFormDataWithDetails()
        {
            try
            {
                Debug.WriteLine("=== Starting detailed validation ===");

                // Check if current user exists
                if (_currentUser == null)
                {
                    Debug.WriteLine("ERROR: _currentUser is null");
                    MessageBox.Show("Internal error: User model is not initialized.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }

                // Call the existing validation method
                bool isValid = ValidateFormData();
                Debug.WriteLine($"Validation result: {isValid}");

                if (isValid)
                {
                    Debug.WriteLine("All validation checks passed");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in ValidateFormDataWithDetails: {ex.Message}");
                MessageBox.Show($"Error during validation: {ex.Message}", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Saves form data to user model with validation
        /// </summary>
        private bool SaveFormDataToUserWithValidation()
        {
            try
            {
                Debug.WriteLine("=== Saving form data to user model ===");

                // Call existing method
                SaveFormDataToUser();

                // Validate that data was saved correctly
                if (string.IsNullOrWhiteSpace(_currentUser.Username))
                {
                    Debug.WriteLine("ERROR: Username not saved to model");
                    return false;
                }

                if (string.IsNullOrWhiteSpace(_currentUser.Email))
                {
                    Debug.WriteLine("ERROR: Email not saved to model");
                    return false;
                }

                Debug.WriteLine($"Form data saved successfully - Username: {_currentUser.Username}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in SaveFormDataToUserWithValidation: {ex.Message}");
                MessageBox.Show($"Error saving form data: {ex.Message}", "Save Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Tests database connection
        /// </summary>
        private bool TestDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("=== Testing database connection ===");

                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    Debug.WriteLine($"Database connection state: {connection.State}");

                    if (connection.State == System.Data.ConnectionState.Open)
                    {
                        Debug.WriteLine("Database connection test successful");
                        return true;
                    }
                    else
                    {
                        Debug.WriteLine($"Database connection failed - State: {connection.State}");
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in TestDatabaseConnection: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves user to database with enhanced error handling
        /// </summary>
        private bool SaveUserToDatabase()
        {
            try
            {
                Debug.WriteLine("=== Saving user to database ===");

                bool success = UserMasterFormRepository.SaveUser(_currentUser);

                if (success)
                {
                    Debug.WriteLine($"User saved successfully with ID: {_currentUser.UserId}");
                }
                else
                {
                    Debug.WriteLine("Database save returned false");
                    MessageBox.Show("Failed to save user to database. Please check the debug output for details.", "Database Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }

                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in SaveUserToDatabase: {ex.Message}");
                MessageBox.Show($"Database error: {ex.Message}", "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Saves edit password with error handling
        /// </summary>
        private bool SaveEditPassword()
        {
            try
            {
                Debug.WriteLine("=== Saving edit password ===");

                string editPasswordHash = PasswordSecurityService.GenerateEditPasswordHash(txtEditPassword.Text);
                bool success = UserMasterFormRepository.UpdateEditPassword(_currentUser.UserId, editPasswordHash);

                if (success)
                {
                    Debug.WriteLine("Edit password saved successfully");
                }
                else
                {
                    Debug.WriteLine("Edit password save failed");
                }

                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in SaveEditPassword: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
